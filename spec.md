Specification Sheet – Multi-Agent AI Developer Assistant
1. Project Overview

We want to build a two-agent AI workflow that assists in software development.

Agent A – Spec Writer: Uses tools (e.g., spec-kit and terminal commands) to generate and refine a specification document.

Agent B – Builder: Reads the specification and begins implementing the application by creating project files, writing code, and installing required dependencies.

The system should be extensible (more agents can be added later, e.g., Tester, Reviewer).

2. Objectives

Automate the creation of a project specification based on a simple prompt.

Translate the generated specification into an initial project scaffold with working code.

Allow agents to use terminal tools (like spec-kit) and manage dependencies (pip, npm, etc.).

Operate locally using models run with Ollama.

Provide results inside Visual Studio Code for developer review.

3. Agents & Roles
3.1 Agent A – Spec Writer

Responsibilities:

Receive a user prompt describing an app idea (e.g., “I want a Todo App”).

Run spec-kit or similar tools in the terminal.

Generate a specification document (Markdown format).

Save the spec file (spec.md) in the project folder.

Tools Required:

Terminal access (CLI).

File writing ability.

Output Example:

# Specification – Todo App
Features:
- Add / Edit / Delete tasks
- Mark tasks as complete
- Save tasks to local storage
Stack:
- Python + Flask
- SQLite database

3.2 Agent B – Builder

Responsibilities:

Read the spec.md document produced by Agent A.

Create project structure (folders, files).

Write initial code (e.g., backend, frontend, config files).

Manage dependencies (requirements.txt, package.json).

Ensure the project can run with minimal setup.

Tools Required:

File system editor (create, edit, delete files).

Package manager (pip, npm, etc.).

Output Example:

/app.py → Flask app

/templates/index.html → basic UI

/requirements.txt → flask, sqlite3

4. Workflow

Input: User gives project idea (“Todo App”).

Spec Writer Agent:

Executes spec-kit with the idea.

Produces spec.md.

Builder Agent:

Reads spec.md.

Creates code files and installs dependencies.

Output:

Project folder with code + spec.md.

Ready-to-run prototype.

5. Technical Requirements

LLM Runtime: Ollama (local models such as LLaMA, Code LLaMA).

Agent Framework: CrewAI (or AutoGen for orchestration).

IDE Integration: VS Code (via AI Toolkit or Continue.dev).

Dependencies:

Python 3.10+

Node.js (optional, if frontend needed)

spec-kit installed via pip (pip install spec-kit)